global over:=1


Esc::
over:=!over
ExitApp

^z::

FileSelectFile, file

Gui, New,+Resize +MinSize220x60,select FileEncoding
Gui, Add, Text,, ѡ��������ļ������ʽ
Gui, Add, ComboBox, vEcodingChoice, UTF-8|ASCII|UTF-16|UTF-8-RAW|UTF-16-RAW
Gui, Add, Text,, �м��ʱ��
Gui, Add, Edit
Gui, Add, UpDown, vMyUpDown Range1-1000, 50
Gui, Add, Text,, �ּ��ʱ��
Gui, Add, Edit
Gui, Add, UpDown, vMyUpDown2 Range1-1000, 20

Gui, Add, Button, w50 h20 y23 default , OK
GuiControl, Text, EcodingChoice, UTF-8

if file =
    Gui, Destroy
else
    Gui, Show
return

GuiClose:
Gui, Destroy
return

ButtonOK:
GuiEscape:
Gui, Submit

MsgBox, The coding form is: %EcodingChoice%
regx = ASCII
IfInString, EcodingChoice, %regx%
{
    EcodingChoice = CP0
}
FileEncoding, %EcodingChoice%


FileRead, text, %file%
SwitchIME(0x04090409)

SetKeyDelay, MyUpDown2

; 清理文本内容，移除换行符和回车符
StringReplace, text, text, `r, , All
StringReplace, text, text, `n, , All

; 移除可能的BOM标记和其他特殊字符
StringReplace, text, text, ﻿, , All  ; 移除UTF-8 BOM
StringReplace, text, text, %A_Tab%, , All  ; 移除制表符

; 清理文本末尾的特殊字符
text := RTrim(text, " `t`r`n)'""")

SendCode(text)
return


SwitchIME(dwLayout){
    HKL:=DllCall("LoadKeyboardLayout", Str, dwLayout, UInt, 1)
    ControlGetFocus,ctl,A
    SendMessage,0x50,0,HKL,%ctl%,A
}

SendCode(text){
    ; 确保文本不为空且已清理
    if (text = "")
        return

    ; 再次清理可能的特殊字符
    text := RegExReplace(text, "[^\x20-\x7E\x80-\xFF]", "")  ; 只保留可打印字符

    Loop, parse, text
    {
        if (!over)
            break

        ; 检查字符是否为有效字符
        if (A_LoopField != "")
        {
            SendRaw, %A_LoopField%
            Sleep, MyUpDown2
        }
    }
}

DeleteThisLine(){
    Send {Enter}
    Sleep, MyUpDown
    Send {Tab}
    Send +{Home}{Backspace}
    ;Sleep, 10
}
