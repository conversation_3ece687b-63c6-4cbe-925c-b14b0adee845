global over:=1


Esc::
over:=!over
ExitApp

^z::

FileSelectFile, file

Gui, New,+Resize +MinSize220x60,select FileEncoding
Gui, Add, Text,, w1
Gui, Add, ComboBox, vEcodingChoice, UTF-8|ASCII|UTF-16|UTF-8-RAW|UTF-16-RAW
Gui, Add, Text,, w2
Gui, Add, Edit
Gui, Add, UpDown, vMyUpDown Range1-1000, 50
Gui, Add, Text,, w3
Gui, Add, Edit
<PERSON>, Add, UpDown, vMyUpDown2 Range1-1000, 20

Gui, Add, Button, w50 h20 y23 default , OK
GuiControl, Text, EcodingChoice, UTF-8

if file =
    Gui, Destroy
else
    Gui, Show
return

GuiClose:
Gui, Destroy
return

ButtonOK:
GuiEscape:
Gui, Submit

MsgBox, The coding form is: %EcodingChoice%
regx = ASCII
IfInString, EcodingChoice, %regx%
{
    EcodingChoice = CP0
}
FileEncoding, %EcodingChoice%


FileRead, text, %file%
SwitchIME(0x04090409)

SetKeyDelay, MyUpDown2

StringReplace, text, text, `r, , All
SendCode(text)

; 运行完成后自动按下 Windows+空格 切换输入法
Send #{Space}
return


SwitchIME(dwLayout){
    HKL:=DllCall("LoadKeyboardLayout", Str, dwLayout, UInt, 1)
    ControlGetFocus,ctl,A
    SendMessage,0x50,0,HKL,%ctl%,A
}

SendCode(text){
    ; 显示文本末尾字符用于调试
    MsgBox,  The coding form is: SubStr(text, -9)

    ; 清理文本末尾可能的多余字符
    text := RTrim(text, " `t`r`n")

    Loop, parse, text, `r`n,
    {
        if (!over)
            break
        DeleteThisLine()
        SendRaw,%A_LoopField%
    }
}

DeleteThisLine(){
    Send {Enter}
    Sleep, MyUpDown
    Send {Tab}
    Send +{Home}{Backspace}
    ;Sleep, 10
}
